<template>
	<page-container ref="pageContainer" :is-show-nav="false" bgColorPage="#E2ECEE">
		<!-- 模糊遮罩 -->
		<view class="blur-overlay" v-if="showContextMenu" @click="hideContextMenu"></view>

		<!-- 悬浮上下文菜单 -->
		<view class="context-menu" v-if="showContextMenu" :style="menuPosition" :class="menuPosition.class">
			<view class="menu-item menu-delete" @click="deleteModel = true">
				<uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
				<text class="menu-text menu-text-delete">{{ $t('common.delete') }}</text>
			</view>
		</view>

		<!-- 添加一个隐藏的临时菜单用于测量高度 -->
		<view
			class="temp-measure-menu context-menu"
			v-if="showTempMenu"
			style="position: absolute; opacity: 0; pointer-events: none; top: -9999px"
		>
			<view class="menu-item menu-delete">
				<uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
				<text class="menu-text menu-text-delete">{{ $t('common.delete') }}</text>
			</view>
		</view>

		<!-- 更多菜单下拉框蒙层 -->
		<view
			class="fixed top-0 left-0 right-0 bottom-0 z-39"
			v-if="showMoreMenu"
			@tap="hideMoreMenu"
			@touchmove.prevent
		></view>

		<!-- 更多菜单下拉框 -->
		<view class="more-menu" v-if="showMoreMenu" :style="moreMenuPosition" @tap.stop>
			<view class="more-menu-item" @tap="startScan">
				<uv-icon name="scan" size="16" color="var(--text-color-secondary)"></uv-icon>
				<text class="more-menu-text">扫一扫</text>
			</view>
			<view class="menu-divider"></view>
			<view class="more-menu-item" @tap="handleSortClick" v-if="configList.length > 0">
				<uv-icon name="order" size="16" color="var(--text-color-secondary)"></uv-icon>
				<text class="more-menu-text">服务器排序</text>
			</view>
			<view class="menu-divider" v-if="configList.length > 0"></view>
			<view class="more-menu-item" @tap="handleHideIPClick">
				<uv-icon
					:name="isIPHidden ? 'eye' : 'eye-fill'"
					size="16"
					color="var(--text-color-secondary)"
				></uv-icon>
				<text class="more-menu-text">{{ isIPHidden ? '显示IP' : '隐藏IP' }}</text>
			</view>
		</view>

		<!-- 克隆项容器 - 放在外层，使用fixed定位 -->
		<view class="fixed-clone-container" v-if="showContextMenu">
			<view class="item-clone-wrapper" :style="clonePosition" v-if="activeServer">
				<view class="server-item server-item-clone" style="margin-top: 0">
					<view class="server-list-container">
						<view class="server-header">
							<view class="server-name">
								<view
									class="server-icon"
									:style="{ backgroundColor: activeServer.status ? '#20a50a' : '#f44336' }"
								></view>
								<text>{{ ` ${activeServer.name} - ${formatIPDisplay(activeServer.ip)}` }}</text>
							</view>
							<view class="server-status">
								<uv-icon name="clock" size="16" style="margin-right: 6rpx"></uv-icon>
								<text class="server-uptime">{{ activeServer.uptime }}</text>
							</view>
						</view>
						<view class="parting-line"></view>
						<view class="server-metrics">
							<!-- 负载指标 -->
							<view class="metric-item">
								<text class="metric-title">{{ $t('server.load') }}</text>
								<ECharts
									:canvas-id="generateSafeCanvasId('clone-load-chart', activeServer.ip)"
									chart-type="gauge"
									:chart-data="getCpuChartData(activeServer.load)"
									:height="110"
								/>
								<view class="metric-detail-text">
									<text>{{ activeServer.load.total }}</text>
								</view>
							</view>

							<!-- CPU 指标 -->
							<view class="metric-item">
								<text class="metric-title">{{ $t('server.cpu') }}</text>
								<ECharts
									:canvas-id="generateSafeCanvasId('clone-cpu-chart', activeServer.ip)"
									chart-type="gauge"
									:chart-data="getCpuChartData(activeServer.cpu)"
									:height="110"
								/>
								<view class="metric-detail-text">
									<text>{{ activeServer.cpu.cores }} {{ $t('linux.cores') }}</text>
								</view>
							</view>

							<!-- 内存指标 -->
							<view class="metric-item">
								<text class="metric-title">{{ $t('server.memory') }}</text>
								<ECharts
									:canvas-id="generateSafeCanvasId('clone-mem-chart', activeServer.ip)"
									chart-type="gauge"
									:chart-data="getMemChartData(activeServer.memory)"
									:height="110"
								/>
								<view class="metric-detail-text">
									<text>{{ activeServer.memory.total }}</text>
								</view>
							</view>

							<!-- 磁盘指标 -->
							<view class="metric-item">
								<text class="metric-title">{{ $t('server.disk') }}(/)</text>
								<ECharts
									:canvas-id="generateSafeCanvasId('clone-disk-chart', activeServer.ip)"
									chart-type="gauge"
									:chart-data="getDiskChartData(activeServer.disk)"
									:height="110"
								/>
								<view class="metric-detail-text">
									<text>{{ activeServer.disk.total }}</text>
								</view>
							</view>

							<!-- 网络指标 -->
							<view class="metric-item">
								<text class="metric-title">{{ $t('server.network') }}</text>
								<view class="network-stats">
									<view class="stats-row">
										<text class="stats-icon">↑</text>
										<text class="stats-value">{{ activeServer.network.up }}KB</text>
									</view>
									<view class="stats-row">
										<text class="stats-icon">↓</text>
										<text class="stats-value">{{ activeServer.network.down }}KB</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<custom-nav :is-back="false" bg-color="#E2ECEE" class="pt-40">
			<template #left>
				<image src="@/static/login/home-logo.png" mode="scaleToFill" class="w-150 h-80 mt-40 mr-20" />
			</template>
			<template #right>
				<view class="mt-40" @click="toggleMoreMenu">
					<uv-icon name="more-dot-fill" color="#909399" size="24"></uv-icon>
				</view>
				<view class="mt-40 ml-30" @click="startScan">
					<uv-icon name="plus-circle" color="#909399" size="32"></uv-icon>
				</view>
			</template>
			<template #nav-title>
				<uv-input
					placeholder="请输入服务器或者IP地址"
					prefixIcon="search"
					prefixIconStyle="font-size: 22px;color: #909399"
					shape="circle"
					border="surround"
					:customStyle="{ backgroundColor: '#fff', height: '60rpx' }"
					class="mt-40"
				></uv-input>
			</template>
		</custom-nav>
		<view class="server-list-container px-16">
			<z-paging
				ref="paging"
				:default-page-size="7"
				use-virtual-list
				:force-close-inner-list="true"
				:auto-hide-loading-after-first-loaded="false"
				:auto-show-system-loading="true"
				:loading-more-enabled="true"
				@query="queryRules"
				@virtualListChange="virtualListChange"
				@refresherStatusChange="onRefresh"
				:refresher-complete-delay="200"
				empty-view-text="暂无服务器，请添加服务器"
			>
				<uni-swipe-action>
					<uni-swipe-action-item
						class="server-list-item mt-16 p-16"
						:class="{ 'server-offline': server.status === false }"
						v-for="(server, index) in virtualListData"
						:id="`zp-id-${server.zp_index}`"
						:key="server.uniqueKey || `server_${server.ip}_${server.rawPath || ''}`"
						@touchstart="handleTouchStart($event)"
						@touchmove="handleTouchMove($event)"
						@touchend="handleTouchEnd($event)"
						@touchcancel="handleTouchCancel($event)"
						:data-index="index"
						:data-server="JSON.stringify(server)"
						:right-options="options"
						@click="onActionClick($event, server)"
					>
						<view
							class="flex items-center justify-between"
							:class="{ 'server-loading': server.isLoadingStatus || server.isManuallyAdding }"
							@click.stop="navigateServer(server)"
						>
							<view class="flex items-center justify-between">
								<view class="relative">
									<image
										src="@/static/server/server-logo.png"
										mode="scaleToFill"
										class="w-80 h-80 mr-20"
									/>
									<view v-if="server.status === true" class="online-indicator"></view>
									<view v-else-if="server.status === false" class="offline-indicator"></view>
									<view v-else class="loading-indicator"></view>
								</view>
								<view class="flex flex-col justify-between">
									<view class="flex items-center">
										<text class="text-30 font-bold">{{ server.name }}</text>
										<template v-if="server.isLoadingStatus">
											<uv-loading-icon
												:show="true"
												mode="spinner"
												size="16"
												color="#999"
												style="margin-left: 10rpx"
											></uv-loading-icon>
											<text class="bg-#999 text-#fff px-8 py-2 rd-4 ml-10 text-24">{{
												$t('server.connecting')
											}}</text>
										</template>
										<template v-else>
											<text
												v-if="server.status === true"
												class="bg-#20a50a text-#fff px-8 py-2 rd-4 ml-10 text-24"
											>
												{{ server.uptime }}
											</text>
											<text
												v-else-if="server.status === false"
												class="bg-#E7E7E7 text-#fff px-8 py-2 rd-4 ml-10 text-24"
											>
												离线
											</text>
											<text v-else class="bg-#999 text-#fff px-8 py-2 rd-4 ml-10 text-24">
												连接中
											</text>
										</template>
									</view>
									<text class="text-24 mt-10 text-#999">IP：{{ formatIPDisplay(server.ip) }}</text>
								</view>
							</view>
							<view
								class="flex items-center justify-between rd-16 px-8 py-4"
								style="border: 3rpx solid rgba(0, 0, 0, 0.1)"
							>
								<image src="@/static/server/arrow.png" mode="scaleToFill" class="w-50 h-50 mr-20" />
								<view class="flex flex-col items-center justify-between">
									<text class="text-20 text-#A7A7A7">{{ server.network.up }}kb/s</text>
									<text class="text-20 mt-10 text-#A7A7A7">{{ server.network.down }}kb/s</text>
								</view>
							</view>
						</view>
						<view class="server-mask" v-show="!server.status">
							<view class="mask-text">{{ $t('server.serverConnectionFailed') }}</view>
						</view>
						<!-- 服务器监控指标网格 -->
						<view class="metrics-grid">
							<!-- 负载指标 - 第1列，跨2行 -->
							<view class="load-card">
								<view class="flex items-center justify-between w-full">
									<text class="load-title">负载</text>
									<text
										class="load-status px-8 py-4 rd-4 text-24"
										:class="getLoadChartStyle(server.load).statusClass"
									>
										{{ getLoadChartStyle(server.load).status }}
									</text>
								</view>
								<view
									class="load-chart-container"
									:style="{ background: server.status === false ? '#E7E7E7' : '#fff' }"
								>
									<view
										class="load-chart-fill"
										:style="{
											height: getLoadChartStyle(server.load).height,
											background: getLoadChartStyle(server.load).background,
										}"
									>
										<text class="load-percentage">{{ getLoadChartStyle(server.load).usage }}%</text>
									</view>
								</view>
							</view>

							<!-- CPU指标 - 第2列第1行 -->
							<view class="metric-item cpu-item">
								<view class="flex flex-col items-center justify-between">
									<text class="metric-title">CPU</text>
									<text class="metric-subtitle px-8 py-4 rd-4 text-24">{{ server.cpu.cores }}核</text>
								</view>
								<ECharts
									:canvas-id="generateSafeCanvasId('cpu-chart', server.ip)"
									chart-type="gauge"
									:chart-data="getCpuChartData(server.cpu, server.status === false)"
									:height="110"
								/>
							</view>

							<!-- 内存指标 - 第3列第1行 -->
							<view class="metric-item memory-item">
								<view class="flex flex-col items-center justify-between">
									<text class="metric-title">内存</text>
									<text class="metric-subtitle px-8 py-4 rd-4 text-24">{{
										server.memory.total
									}}</text>
								</view>
								<ECharts
									:canvas-id="generateSafeCanvasId('memory-chart', server.ip)"
									chart-type="gauge"
									:chart-data="getMemChartData(server.memory, server.status === false)"
									:height="110"
								/>
							</view>

							<!-- 磁盘指标 - 第2-3列第2行 -->
							<view class="disk-item">
								<view class="disk-header mb-20">
									<text class="disk-title">磁盘(/)</text>
									<text
										class="disk-status px-8 py-4 rd-4 text-24"
										:class="getDiskChartStyle(server.disk).statusClass"
									>
										{{ getDiskChartStyle(server.disk).status }}
									</text>
								</view>
								<view class="disk-progress-container">
									<view
										class="disk-progress-bar"
										:style="{ background: server.status === false ? '#E7E7E7' : '#fff' }"
									>
										<view
											class="disk-progress-fill"
											:style="{
												width: getDiskChartStyle(server.disk).width,
												background: getDiskChartStyle(server.disk).background,
											}"
										></view>
									</view>
									<text
										class="disk-percentage"
										:style="{ color: getDiskChartStyle(server.disk).color }"
										>{{ getDiskChartStyle(server.disk).usage }}%</text
									>
								</view>
							</view>
						</view>
					</uni-swipe-action-item>
				</uni-swipe-action>
			</z-paging>
		</view>
		<CustomDialog
			v-model="deleteModel"
			contentHeight="140rpx"
			:title="$t('common.deleteConfirm')"
			@close="deleteModel = false"
			@confirm="confirmDelete"
			:confirmStyle="{
				backgroundColor: '#FF3B30',
			}"
		>
			<view class="py-20 text-secondary">
				<text>{{ $t('server.deleteConfirmText', { ip: formatIPDisplay(activeServer?.ip) }) }}</text>
			</view>
		</CustomDialog>
		<CustomDialog
			v-model="iosPromptModel"
			contentHeight="200rpx"
			title="绑定提示"
			@close="iosPromptModel = false"
			@confirm="confirmIosPrompt"
			:confirmText="$t('server.iosPrompt')"
			cancelText="重新绑定"
		>
			<view class="text-secondary flex flex-col justify-center h-full">
				<view>● 苹果手机无法信任自签证书的原因，导致扫码绑定失败</view>
				<view>● 点击查看教程里面有解决方案！</view>
			</view>
		</CustomDialog>
		<CustomDialog
			v-model="harmonyPromptModel"
			contentHeight="200rpx"
			title="绑定提示"
			@close="harmonyPromptModel = false"
			@confirm="confirmHarmonyPrompt"
			confirmText="开启并绑定"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				由于HarmonyOS系统限制，是否开启跳过网络SSL证书验证绑定？
			</view>
		</CustomDialog>
	</page-container>

	<uni-fab
		:pattern="{ buttonColor: '#20a50a' }"
		horizontal="right"
		vertical="bottom"
		v-show="virtualListData.length > 0"
		@click="startScan"
		:popMenu="false"
	>
	</uni-fab>
</template>

<script setup>
	import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
	import { onShow, onHide, onLoad, onUnload } from '@dcloudio/uni-app';
	import PageContainer from '@/components/pageContainer/index.vue';
	import CustomNav from '@/components/customNav/index.vue';
	import ECharts from '@/components/ECharts/index.vue';
	import CustomDialog from '@/components/customDialog/index.vue';
	import {
		pageContainer,
		getCpuChartData,
		getMemChartData,
		getDiskChartData,
		getLoadChartStyle,
		getDiskChartStyle,
		getServerList,
		useServerListPolling,
		formatIPDisplay,
		generateSafeCanvasId,
		onActionDelete,
		handleBindSuccess,
		handleCloudBindSuccess,
		// 更多菜单相关
		showMoreMenu,
		moreMenuPosition,
		toggleMoreMenu,
		hideMoreMenu,
		handleSortClick,
		handleHideIPClick,
		// IP隐藏相关
		isIPHidden,
		initIPHiddenState,
		// 分页相关
		paging,
		virtualListData,
		getIsManuallyAddingServer,
		isDeletingServer,
		resetFirstScreenLoadState,
	} from './useController';
	import { useConfigStore } from '@/store';
	import { useServerListStore } from './store';
	import useScan from '@/hooks/useScan';
	import { $t } from '@/locale/index.js';

	const { phoneBrand, phoneModel, configList } = useConfigStore().getReactiveState();
	const { serverList } = useServerListStore().getReactiveState();

	// 长按菜单相关状态
	const showContextMenu = ref(false);
	const showTempMenu = ref(false);
	const menuPosition = ref({});
	const activeServer = ref(null);
	const clonePosition = ref({});
	const touchStartTime = ref(0);
	const touchStartPos = ref({ x: 0, y: 0 });
	const longPressTimer = ref(null);
	const isLongPress = ref(false);
	const touchMoved = ref(false);

	// 对话框状态
	const deleteModel = ref(false);
	const iosPromptModel = ref(false);
	const harmonyPromptModel = ref(false);

	// 滑动操作选项
	const options = ref([{ text: '删除', style: { backgroundColor: '#dd2f00' } }]);

	// 获取轮询方法
	const { startPolling, stopPolling } = useServerListPolling();

	// 扫码功能增强
	const { isScanning, startScan } = useScan({
		onScanError(error) {
			// 根据平台显示不同的错误处理
			if (phoneBrand.value === 'Apple') {
				iosPromptModel.value = true;
			} else if (phoneBrand.value === 'HUAWEI' || phoneBrand.value === 'HONOR') {
				harmonyPromptModel.value = true;
			} else {
				pageContainer.value.notify.error(error.message || '扫码失败');
			}
		},
		onScanSuccess(res) {
			// 扫码成功处理
			// 这里可以添加扫码成功后的处理逻辑
		},
		onBindSuccess(bindInfo) {
			// 绑定成功处理
			// 调用 useController.js 中的 handleBindSuccess 函数处理绑定成功的逻辑
			handleBindSuccess(bindInfo);
			pageContainer.value.notify.success('绑定成功');
		},
		onBindError(error) {
			// 根据平台显示不同的错误处理
			if (phoneBrand.value === 'Apple') {
				iosPromptModel.value = true;
			} else if (phoneBrand.value === 'HUAWEI' || phoneBrand.value === 'HONOR') {
				harmonyPromptModel.value = true;
			} else {
				pageContainer.value.notify.error(error.message || '绑定失败');
			}
		},
		onLoginSuccess(loginInfo) {
			pageContainer.value.notify.success('登录成功');
		},
		onLoginError(error) {
			// 根据平台显示不同的错误处理
			if (phoneBrand.value === 'Apple') {
				iosPromptModel.value = true;
			} else if (phoneBrand.value === 'HUAWEI' || phoneBrand.value === 'HONOR') {
				harmonyPromptModel.value = true;
			} else {
				pageContainer.value.notify.error(error.message || '登录失败');
			}
		},
		getDeviceInfo: () => {
			return {
				brand: phoneBrand.value,
				model: phoneModel.value,
			};
		},
	});

	/**
	 * 列表数据变更 - 优化虚拟列表数据同步（并行加载优化版本）
	 */
	const virtualListChange = (vList) => {
		// 将虚拟列表数据存储到专用的数组中，而不是直接赋值给serverList.value
		if (Array.isArray(vList) && vList.length >= 0) {
			// 【关键修复】如果正在删除服务器，暂时跳过虚拟列表同步，避免数据竞争
			if (isDeletingServer.value) {
				return;
			}

			// 确保虚拟列表数据与主列表数据状态一致
			// 完整同步所有服务器数据，避免分页时数据重置
			const { serverList } = useServerListStore().getReactiveState();

			// 【修复】增加数据一致性检查，确保虚拟列表长度不超过主列表
			if (vList.length > serverList.value.length) {
				// 去重处理：基于uniqueKey去除重复项
				const uniqueVList = [];
				const seenKeys = new Set();

				for (const vItem of vList) {
					// 使用uniqueKey作为主要标识符，如果不存在则使用rawPath作为回退
					const key = vItem.uniqueKey || vItem.rawPath;
					if (!seenKeys.has(key)) {
						seenKeys.add(key);
						uniqueVList.push(vItem);
					}
				}
				vList = uniqueVList;
			}

			const syncedVList = vList.map((vItem) => {
				// 查找主列表中对应的服务器数据 - 基于SECRET值（uniqueKey）进行匹配
				const mainListItem = serverList.value.find(
					(server) => server.uniqueKey === vItem.uniqueKey || server.rawPath === vItem.rawPath,
				);

				// 如果主列表中有更完整的数据，使用主列表的完整数据
				if (mainListItem) {
					// 【并行加载优化】完整同步所有字段，确保并行加载的数据能够及时更新
					// 特别注意保持最新的加载状态和数据
					return {
						...mainListItem, // 使用主列表的完整数据
						zp_index: vItem.zp_index, // 保持虚拟列表的索引
						// 【新增】确保并行加载的最新状态得到保持
						isLoadingStatus: mainListItem.isLoadingStatus,
						status: mainListItem.status,
						name: mainListItem.name,
						uptime: mainListItem.uptime,
						cpu: mainListItem.cpu,
						memory: mainListItem.memory,
						disk: mainListItem.disk,
						network: mainListItem.network,
						load: mainListItem.load,
					};
				}

				// 如果主列表中没有对应数据，保持虚拟列表原有数据
				return vItem;
			});

			// 【修复】最终去重检查，确保虚拟列表中没有重复项
			const finalUniqueList = [];
			const finalSeenKeys = new Set();

			for (const item of syncedVList) {
				const key = item.uniqueKey || `${item.ip}_${item.rawPath}`;
				if (!finalSeenKeys.has(key)) {
					finalSeenKeys.add(key);
					finalUniqueList.push(item);
				}
			}

			// 更新虚拟列表数据
			virtualListData.value = finalUniqueList;
		}
	};

	/**
	 * 查询规则列表 - 使用本地分页
	 */
	const queryRules = async (page, pageSize) => {
		try {
			// 如果正在手动添加服务器，跳过刷新以避免干扰
			if (getIsManuallyAddingServer()) {
				paging.value.setLocalPaging(virtualListData.value);
				return;
			}

			// 区分首次加载和分页加载
			const isFirstLoad = page === 1;

			if (isFirstLoad) {
				// 首次加载：获取完整的服务器列表数据
				const res = await getServerList();
				// 确保返回的数据是数组格式
				const validData = Array.isArray(res) ? res : [];
				// 使用本地分页功能，将完整数据传给z-paging进行本地分页处理
				paging.value.setLocalPaging(validData);
			} else {
				// 分页加载：避免重新调用setLocalPaging，直接使用现有虚拟列表数据
				// 这样可以避免z-paging重新处理数据导致的重置问题
				const { serverList } = useServerListStore().getReactiveState();

				// 检查是否有新的服务器数据需要更新到虚拟列表
				if (virtualListData.value && virtualListData.value.length > 0) {
					// 确保虚拟列表数据与主列表数据同步，但不触发重新分页
					const updatedVirtualData = virtualListData.value.map((vItem) => {
						const mainListItem = serverList.value.find(
							(server) =>
								server.uniqueKey === vItem.uniqueKey ||
								(server.ip === vItem.ip && server.rawPath === vItem.rawPath),
						);

						if (mainListItem) {
							return {
								...mainListItem,
								zp_index: vItem.zp_index, // 保持虚拟列表索引
							};
						}
						return vItem;
					});

					virtualListData.value = updatedVirtualData;
					// 跳过setLocalPaging调用，避免数据重置
					return;
				}

				// 如果虚拟列表为空，则使用主列表数据进行分页
				const currentData = Array.isArray(serverList.value) ? serverList.value : [];
				paging.value.setLocalPaging(currentData);
			}

			// 无论是否有排序设置，都需要触发虚拟列表更新以确保排序后的数据正确显示
			setTimeout(() => {
				if (paging.value && paging.value.updateVirtualListRender) {
					paging.value.updateVirtualListRender();
				}
			}, 50);
		} catch (error) {
			paging.value.setLocalPaging(false);
		}
	};

	const onRefresh = async (reloadType) => {
		if (reloadType === 'complete') {
			// 【修复】下拉刷新完成时重置轮询状态，清除失败计数器
			// resetPolling();
			pageContainer.value.notify.success($t('common.refreshSuccess'));
		}
	};

	// 触摸事件处理 (UniApp 兼容版本)
	const handleTouchStart = (event) => {
		const touch = event.touches[0];
		touchStartTime.value = Date.now();
		touchStartPos.value = {
			x: touch.clientX || touch.pageX,
			y: touch.clientY || touch.pageY,
		};
		touchMoved.value = false;
		isLongPress.value = false;

		// 获取当前触摸的服务器数据
		const target = event.currentTarget;
		const serverData = target.dataset?.server;
		if (serverData) {
			try {
				activeServer.value = JSON.parse(serverData);
			} catch (e) {
				console.error('解析服务器数据失败:', e);
				// 从事件中获取服务器索引作为备选方案
				const index = target.dataset?.index;
				if (index !== undefined && virtualListData.value[index]) {
					activeServer.value = virtualListData.value[index];
				}
				return;
			}
		}

		// 设置长按定时器
		longPressTimer.value = setTimeout(() => {
			if (!touchMoved.value) {
				isLongPress.value = true;
				showLongPressMenu(event);
			}
		}, 500); // 500ms 长按触发
	};

	const handleTouchMove = (event) => {
		const touch = event.touches[0];
		const currentX = touch.clientX || touch.pageX;
		const currentY = touch.clientY || touch.pageY;
		const deltaX = Math.abs(currentX - touchStartPos.value.x);
		const deltaY = Math.abs(currentY - touchStartPos.value.y);

		// 如果移动距离超过阈值，取消长按
		if (deltaX > 10 || deltaY > 10) {
			touchMoved.value = true;
			if (longPressTimer.value) {
				clearTimeout(longPressTimer.value);
				longPressTimer.value = null;
			}
		}
	};

	const handleTouchEnd = (event) => {
		if (longPressTimer.value) {
			clearTimeout(longPressTimer.value);
			longPressTimer.value = null;
		}

		// 如果不是长按且没有移动，执行点击事件
		if (!isLongPress.value && !touchMoved.value) {
			// 这里可以添加点击事件处理
		}
	};

	const handleTouchCancel = (event) => {
		if (longPressTimer.value) {
			clearTimeout(longPressTimer.value);
			longPressTimer.value = null;
		}
		touchMoved.value = false;
		isLongPress.value = false;
	};

	// 显示长按菜单
	const showLongPressMenu = async (event) => {
		if (!activeServer.value) return;

		const touch = event.touches[0];

		// UniApp 环境下获取系统信息
		const systemInfo = uni.getSystemInfoSync();
		const screenWidth = systemInfo.windowWidth;
		const screenHeight = systemInfo.windowHeight;

		// 计算克隆项位置 (简化版本，适用于 UniApp)
		clonePosition.value = {
			position: 'fixed',
			top: '50%',
			left: '50%',
			transform: 'translate(-50%, -50%)',
			zIndex: 1000,
		};

		// 先显示临时菜单来测量高度
		showTempMenu.value = true;
		await nextTick();

		// 估算菜单高度 (UniApp 环境下)
		const menuHeight = 60; // 固定高度，避免 DOM 查询

		showTempMenu.value = false;

		// 计算菜单位置
		const menuX = touch.clientX || touch.pageX || screenWidth / 2;
		const menuY = (touch.clientY || touch.pageY || screenHeight / 2) - menuHeight - 10;

		// 确保菜单不超出屏幕边界
		const menuWidth = 120; // 估算菜单宽度

		let finalX = menuX;
		let finalY = menuY;

		if (finalX + menuWidth > screenWidth) {
			finalX = screenWidth - menuWidth - 10;
		}
		if (finalX < 10) {
			finalX = 10;
		}
		if (finalY < 10) {
			finalY = (touch.clientY || touch.pageY || screenHeight / 2) + 10;
		}
		if (finalY + menuHeight > screenHeight) {
			finalY = screenHeight - menuHeight - 10;
		}

		menuPosition.value = {
			position: 'fixed',
			left: finalX + 'px',
			top: finalY + 'px',
			zIndex: 1001,
		};

		showContextMenu.value = true;
	};

	// 隐藏上下文菜单
	const hideContextMenu = () => {
		showContextMenu.value = false;
		activeServer.value = null;
		clonePosition.value = {};
		menuPosition.value = {};
	};

	// 服务器导航
	const navigateServer = (server) => {
		if (server.status === false) {
			pageContainer.value.notify.error('服务器离线，无法访问');
			return;
		}

		// 导航到服务器详情页面
		uni.navigateTo({
			url: `/pages/index/linux/index?ip=${server.ip}&name=${encodeURIComponent(server.name)}&secret=${
				server.secret
			}`,
		});
	};

	// 删除服务器
	const onDeleteServer = (server) => {
		// 调用 useController.js 中的删除服务器方法
		onActionDelete(server);
	};

	// 滑动操作点击事件
	const onActionClick = (event, server) => {
		if (event.content.text === '删除') {
			activeServer.value = server;
			deleteModel.value = true;
		}
	};

	// 确认删除
	const confirmDelete = () => {
		if (activeServer.value) {
			onActionDelete(activeServer.value);
			deleteModel.value = false;
			hideContextMenu();
		}
	};

	// iOS 提示确认
	const confirmIosPrompt = () => {
		iosPromptModel.value = false;
		// 跳转到教程页面
		uni.navigateTo({
			url: '/pages/index/tutorial/index',
		});
	};

	// HarmonyOS 提示确认
	const confirmHarmonyPrompt = () => {
		harmonyPromptModel.value = false;
		// 开启跳过SSL验证并重新扫码
		// 这里可以添加相关逻辑
		startScan();
	};

	// Unicode 解码函数 (HarmonyOS 特殊处理)
	const decodeUnicode = (str) => {
		if (!str) return str;
		try {
			return str.replace(/\\u[\dA-F]{4}/gi, (match) => {
				return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
			});
		} catch (e) {
			console.error('Unicode 解码失败:', e);
			return str;
		}
	};

	// 页面生命周期
	onLoad((options) => {
		// 初始化IP隐藏状态
		initIPHiddenState();

		// HarmonyOS 特殊处理
		if (phoneBrand.value === 'HUAWEI' || phoneBrand.value === 'HONOR') {
			// 处理 HarmonyOS 的特殊字符编码问题
			if (options.name) {
				options.name = decodeUnicode(options.name);
			}
		}
	});

	onShow(() => {
		startPolling();
		// 隐藏所有菜单
		hideMoreMenu();
		hideContextMenu();

		// 从排序页面返回时需要刷新列表以应用新的排序
		// 检查是否有排序更改标记
		const sortOrderChanged = uni.getStorageSync('sortOrderChanged');
		const savedSortOrder = uni.getStorageSync('serverSortOrder');

		if (sortOrderChanged && savedSortOrder && Array.isArray(savedSortOrder) && savedSortOrder.length > 0) {
			// 清除标记
			uni.removeStorageSync('sortOrderChanged');
			// 重新加载服务器列表以应用新的排序
			getServerList();
		}
	});

	onHide(() => {
		stopPolling();
		hideMoreMenu();
		hideContextMenu();
	});

	onUnload(() => {
		// 清理定时器
		if (longPressTimer.value) {
			clearTimeout(longPressTimer.value);
			longPressTimer.value = null;
		}
		stopPolling();
	});

	onMounted(() => {
		// 页面挂载时隐藏更多菜单
		getDevicesInfoEvent();
		// 测量菜单高度
		measureMenuHeight();
		// 初始化IP隐藏状态
		initIPHiddenState();
	});

	onUnmounted(() => {
		stopPolling();
	});

	onLoad(() => {
		// 重置首屏加载状态，确保每次进入页面都能触发首屏加载优化
		resetFirstScreenLoadState();
		// 页面加载时获取服务器列表
		getServerList();
		// 初始化IP隐藏状态
		initIPHiddenState();
	});
</script>
<style lang="scss" scoped>
	.server-list-item {
		background: var(--bg-color);
		border-radius: 32rpx;
		transition: all 0.3s ease;
	}

	/* 【新增】离线状态下的文字颜色调整 */
	.server-offline .text-30,
	.server-offline .text-24,
	.server-offline .metric-title,
	.server-offline .metric-subtitle,
	.server-offline .load-title,
	.server-offline .disk-title {
		color: #999999 !important;
	}

	/* 在线状态指示器 */
	.online-indicator {
		position: absolute;
		bottom: 10rpx;
		right: 15rpx;
		width: 20rpx;
		height: 20rpx;
		background-color: #20a50a;
		border-radius: 50%;
		border: 2rpx solid #ffffff;
		box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.2);
		animation: ripple-online 2s infinite;
	}

	/* 【新增】离线状态指示器 */
	.offline-indicator {
		position: absolute;
		bottom: 10rpx;
		right: 15rpx;
		width: 20rpx;
		height: 20rpx;
		background-color: #e7e7e7;
		border-radius: 50%;
		border: 2rpx solid #ffffff;
		box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.2);
	}

	/* 【新增】加载状态指示器 */
	.loading-indicator {
		position: absolute;
		bottom: 10rpx;
		right: 15rpx;
		width: 20rpx;
		height: 20rpx;
		background-color: #999;
		border-radius: 50%;
		border: 2rpx solid #ffffff;
		box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.2);
		animation: pulse 1.5s infinite;
	}

	@keyframes ripple-online {
		0% {
			box-shadow: 0 0 0 0 rgba(32, 165, 10, 0.3);
		}
		70% {
			box-shadow: 0 0 0 12rpx rgba(32, 165, 10, 0);
		}
		100% {
			box-shadow: 0 0 0 0 rgba(32, 165, 10, 0);
		}
	}

	/* 【新增】加载状态的脉冲动画 */
	@keyframes pulse {
		0%,
		100% {
			opacity: 1;
		}
		50% {
			opacity: 0.5;
		}
	}

	/* 监控指标网格布局 */
	.metrics-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-template-rows: repeat(2, 1fr);
		gap: 8rpx;
		margin-top: 32rpx;
		height: 300rpx; /* 设置固定高度确保2行布局 */
	}

	/* 负载卡片 - 第1列，跨2行 */
	.load-card {
		grid-column: 1;
		grid-row: 1 / 3;
		background: rgba(247, 247, 247, 1);
		border-radius: 24rpx;
		padding: 24rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		transition: all 0.3s ease;
	}

	.load-title {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 8rpx;
		align-self: flex-start;
	}

	/* 【优化】负载状态样式 - 支持动态状态类 */
	.load-status {
		font-size: 24rpx;
		margin-bottom: 24rpx;
		align-self: flex-start;
		border-radius: 8rpx;
		transition: all 0.3s ease;
	}

	/* 【新增】负载状态的不同级别样式 */
	.load-status-normal {
		color: rgba(32, 165, 58, 1);
		background: rgba(32, 165, 58, 0.1);
	}

	.load-status-medium {
		color: rgba(255, 193, 7, 1);
		background: rgba(255, 193, 7, 0.1);
	}

	.load-status-high {
		color: rgba(211, 47, 47, 1);
		background: rgba(211, 47, 47, 0.1);
	}

	/* 【优化】柱形图容器 */
	.load-chart-container {
		flex: 1;
		width: 100rpx;
		position: relative;
		background: #fff;
		border-radius: 16rpx;
		position: relative;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		overflow: hidden;
		border: 2rpx solid rgba(0, 0, 0, 0.1);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	/* 【优化】柱形图填充 - 支持动态高度和颜色，添加过渡动画 */
	.load-chart-fill {
		width: 100%;
		min-height: 5%; /* 最小高度保证可见性 */
		border-radius: 0 0 16rpx 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		/* 【关键】CSS3过渡动画 - 支持高度和背景色的平滑过渡 */
		transition:
			height 0.8s cubic-bezier(0.4, 0, 0.2, 1),
			background 0.6s ease-in-out;
		/* 【新增】增加轻微的阴影效果，提升视觉层次 */
		box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	/* 【优化】负载百分比文字 - 添加动画效果 */
	.load-percentage {
		font-size: 28rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
		transition: transform 0.3s ease;
		position: absolute;
		bottom: 30rpx;
		left: 50%;
		transform: translateX(-50%);
	}

	/* 【新增】当负载值变化时的微动画效果 */
	.load-percentage:hover {
		transform: scale(1.05);
	}

	.metric-item {
		background: rgba(247, 247, 247, 1);
		border-radius: 16rpx;
		padding: 20rpx 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* CPU指标 - 第2列第1行 */
	.cpu-item {
		grid-column: 2;
		grid-row: 1;
	}

	/* 内存指标 - 第3列第1行 */
	.memory-item {
		grid-column: 3;
		grid-row: 1;
	}

	.metric-title {
		font-size: 24rpx;
		color: #333;
		margin-bottom: 4rpx;
	}

	.metric-subtitle {
		font-size: 20rpx;
		color: rgba(32, 165, 58, 1);
		background: rgba(32, 165, 58, 0.1);
	}

	/* 磁盘指标 - 第2-3列第2行 */
	.disk-item {
		grid-column: 2 / 4;
		grid-row: 2;
		background: rgba(247, 247, 247, 1);
		border-radius: 16rpx;
		padding: 20rpx 24rpx;
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}

	.disk-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.disk-title {
		font-size: 24rpx;
		color: #333;
	}

	.disk-total {
		font-size: 20rpx;
		color: rgba(32, 165, 58, 1);
		background: rgba(32, 165, 58, 0.1);
	}

	/* 【新增】磁盘状态的不同级别样式 */
	.disk-status {
		font-size: 20rpx;
		border-radius: 8rpx;
		transition: all 0.3s ease;
	}

	.disk-status-normal {
		color: rgba(32, 165, 58, 1);
		background: rgba(32, 165, 58, 0.1);
	}

	.disk-status-medium {
		color: rgba(255, 193, 7, 1);
		background: rgba(255, 193, 7, 0.1);
	}

	.disk-status-high {
		color: rgba(255, 152, 0, 1);
		background: rgba(255, 152, 0, 0.1);
	}

	.disk-status-critical {
		color: rgba(211, 47, 47, 1);
		background: rgba(211, 47, 47, 0.1);
	}

	.disk-progress-container {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.disk-progress-bar {
		flex: 1;
		height: 16rpx;
		background: #fff;
		border-radius: 8rpx;
		overflow: hidden;
		position: relative;
		border: 2rpx solid rgba(0, 0, 0, 0.1);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.disk-progress-fill {
		height: 100%;
		border-radius: 8rpx;
		/* 【关键】CSS3过渡动画 - 支持宽度和背景色的平滑过渡 */
		transition:
			width 0.8s cubic-bezier(0.4, 0, 0.2, 1),
			background 0.6s ease-in-out;
		min-width: 5%; /* 最小宽度保证可见性 */
		/* 【新增】增加轻微的阴影效果，提升视觉层次 */
		box-shadow: 0 0 4rpx rgba(0, 0, 0, 0.1);
	}

	.disk-percentage {
		font-size: 24rpx;
		font-weight: bold;
		color: #20a50a;
		min-width: 60rpx;
		text-align: right;
		/* 【新增】当磁盘值变化时的微动画效果 */
		transition: color 0.3s ease;
	}

	/* 模糊遮罩样式 */
	.blur-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.3);
		backdrop-filter: blur(5px);
		z-index: 999;
		animation: fadeIn 0.2s ease;
	}

	/* 上下文菜单样式 */
	.context-menu {
		position: fixed;
		background: #ffffff;
		border-radius: 12rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
		padding: 8rpx 0;
		min-width: 160rpx;
		z-index: 1001;
		animation: contextMenuShow 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.menu-item {
		display: flex;
		align-items: center;
		padding: 16rpx 24rpx;
		cursor: pointer;
		transition: background-color 0.2s ease;
	}

	.menu-item:hover {
		background-color: #f5f5f5;
	}

	.menu-item:active {
		background-color: #e0e0e0;
	}

	.menu-delete {
		color: #ff3b30;
	}

	.menu-text {
		margin-left: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
	}

	.menu-text-delete {
		color: #ff3b30;
	}

	/* 克隆项容器样式 */
	.fixed-clone-container {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;
		z-index: 1000;
	}

	.item-clone-wrapper {
		pointer-events: none;
	}

	.server-item-clone {
		opacity: 0.8;
		transform: scale(1.02);
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
		border: 2rpx solid #20a50a;
		animation: cloneItemPulse 0.3s ease;
	}

	/* 服务器遮罩样式 */
	.server-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 16rpx;
		z-index: 10;
	}

	.mask-text {
		color: #ffffff;
		font-size: 28rpx;
		font-weight: 500;
		text-align: center;
	}

	/* 加载状态样式 */
	.server-loading {
		opacity: 0.7;
		transition: opacity 0.3s ease;
	}

	/* 动画效果 */
	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	@keyframes contextMenuShow {
		from {
			opacity: 0;
			transform: scale(0.8) translateY(-10rpx);
		}
		to {
			opacity: 1;
			transform: scale(1) translateY(0);
		}
	}

	@keyframes cloneItemPulse {
		0% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.05);
		}
		100% {
			transform: scale(1.02);
		}
	}

	/* 更多菜单样式 */
	.more-menu {
		position: fixed;
		z-index: 40;
		background: #ffffff;
		border-radius: 14rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.25);
		padding: 10rpx 0;
		min-width: 240rpx;
		animation: fadeInDown 0.2s ease;
		transform-origin: top right;
	}

	.more-menu-item {
		display: flex;
		align-items: center;
		padding: 24rpx 30rpx;
		transition: background-color 0.15s ease;
	}

	.more-menu-item:active {
		background-color: rgba(0, 0, 0, 0.05);
	}

	.more-menu-text {
		margin-left: 20rpx;
		font-size: 28rpx;
		font-weight: 400;
		color: #333333;
	}

	.menu-divider {
		height: 1rpx;
		background-color: rgba(0, 0, 0, 0.1);
		margin: 0 10rpx;
	}

	/* 修改下拉菜单动画，只在垂直方向有效 */
	@keyframes fadeInDown {
		from {
			opacity: 0;
			transform: translateY(-10rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
</style>
